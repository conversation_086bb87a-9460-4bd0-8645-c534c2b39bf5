<script setup>
import { talkRecord } from '@/hooks/useAiRequest'
import { computed } from 'vue'
const lastData = computed(() => {
  return talkRecord.value[talkRecord.value.length - 1]
})
</script>
<template>
  <div class="record-container" v-if="lastData">
    <div class="user">
      <img src="@/assets/images/default-photo.png" alt="" />
      <span>{{ lastData.user }}</span>
    </div>
    <div class="ai">
      <span>{{ lastData.ai }}</span>
      <img src="@/assets/images/response-photo.png" alt="" />
    </div>
  </div>
</template>
<style scoped lang="scss">
.record-container {
  padding: 48px;
  .user {
    display: flex;
    align-items: flex-end;
    img {
      width: 80px;
      height: 80px;
      margin-right: 20px;
    }
    span {
      display: flex;
      align-items: center;
      width: 640px;
      height: 120px;
      padding: 0 40px;
      background: linear-gradient(271deg, #dae8ff 0%, #ccdfff 100%);
      box-shadow: 0px 4px 4px 0px rgba(144, 140, 140, 0.25);
      border-radius: 60px 60px 60px 0;
      border: 1px solid #ffffff;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 30px;
      color: #333333;
    }
  }
  .ai {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    margin-top: 30px;
    img {
      width: 80px;
      height: 80px;
      margin-left: 20px;
    }
    span {
      width: 666px;
      max-height: 290px;
      padding: 35px 50px;

      background: #466488;
      border-radius: 60px 60px 0px 60px;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 30px;
      color: #ffffff;
      line-height: 38px;
    }
  }
}
</style>
