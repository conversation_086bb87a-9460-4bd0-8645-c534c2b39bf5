# 按键录音问题解决方案

## 问题描述

用户希望实现的逻辑：
1. 按住 F2 进行语音监听，实时转文字
2. 松开 F2 后，将转好的文字发给 AI
3. 当 `state === 1` 时代表文字被校验矫正过了，然后发出去

**遇到的问题：**
1. 按住 F2 不放，说一段话后停止，由于是实时的转写校验矫正，导致没松开 F2 就执行了发送功能
2. 如果加上 `isKeyListening` 判断条件，又会出现松开 F2 时最后转写的文字并不是校验矫正好的，导致文字消失，也不会往下执行发送消息的操作

## 问题根本原因

### FunASR 工作机制
- **实时识别** (`state: 0`): 返回临时的、未校验的识别文字
- **校验完成** (`state: 1`): 返回经过校验矫正的最终文字，有 `stamp_sents` 字段

### 时序问题
1. 用户按住 F2 说话时，FunASR 会在说话过程中多次返回 `state: 1` 的校验完成文字
2. 原代码逻辑：收到 `state: 1` 就立即发送 AI 请求，导致还没松开 F2 就触发了发送
3. 如果简单加 `isKeyListening` 判断：松开 F2 时，最后的校验文字可能还没返回，导致使用了未校验的文字或丢失文字

## 解决方案

### 核心思路
1. **按键录音模式标识**: 在 WebSocket 层面添加按键录音模式标识
2. **文字缓存机制**: 在按键录音期间，缓存所有的校验完成文字，不立即发送 AI 请求
3. **延迟处理**: 松开 F2 后，延迟处理最终结果，确保最后的校验文字能够返回

### 技术实现

#### 1. WebSocket 连接器改进 (`src/utils/wsconnecter.js`)

```javascript
// 添加按键录音模式状态
var isKeyRecordingMode = false

// 设置按键录音模式
this.setKeyRecordingMode = function (enabled) {
  isKeyRecordingMode = enabled
  console.log('设置按键录音模式:', enabled)
}

// 消息处理 - 添加按键录音模式标识
if (data.stamp_sents) {
  const messageData = { 
    state: 1, 
    text: data.text,
    isKeyRecording: isKeyRecordingMode // 添加标识
  }
  useSocketStore().msgHandle(messageData)
}
```

#### 2. 语音监听逻辑改进 (`src/views/talk/hooks/useListen.js`)

```javascript
// 按键录音期间缓存的最终文字
const keyRecordingFinalText = ref('')

// 监听 WebSocket 数据
watch(() => socketStore.lastData, (newData) => {
  if (newData && newData.text) {
    listenText.value = newData.text
    
    if (newData.state === 1) {
      // 如果是按键录音模式，缓存最终文字，不立即发送AI请求
      if (newData.isKeyRecording) {
        keyRecordingFinalText.value = newData.text
        console.log('按键录音模式 - 缓存最终文字:', keyRecordingFinalText.value)
      } else {
        // 非按键录音模式，正常进行AI请求
        aiRequestHandle()
      }
    }
  }
})

// 按键监听处理
const handleVoiceListening = (_, data) => {
  if (data.action === 'start') {
    // 开始按键录音
    isKeyListening.value = true
    keyRecordingFinalText.value = '' // 清空缓存
    socketStore.setKeyRecordingMode(true) // 设置按键录音模式
    socketStore.resetRecognition()
    setTimeout(() => recStart(), 100)
    
  } else if (data.action === 'stop') {
    // 停止按键录音
    isKeyListening.value = false
    socketStore.setKeyRecordingMode(false) // 关闭按键录音模式
    recStop()
    
    // 延迟处理最终结果，确保最后的识别结果能够返回
    setTimeout(() => {
      if (keyRecordingFinalText.value.trim()) {
        // 使用缓存的最终校验文字
        aiRequestHandle(keyRecordingFinalText.value)
      } else if (listenText.value.trim()) {
        // 备用方案：使用当前文字
        aiRequestHandle(listenText.value)
      }
      keyRecordingFinalText.value = '' // 清空缓存
    }, 500) // 等待500ms
  }
}
```

#### 3. Store 层改进 (`src/stores/ws.js`)

```javascript
// 添加按键录音模式控制方法
function setKeyRecordingMode(enabled) {
  socket.setKeyRecordingMode(enabled)
}
```

## 解决效果

### ✅ 问题解决
1. **避免提前触发**: 按键录音期间，校验完成的文字被缓存，不会立即触发 AI 请求
2. **确保文字完整**: 松开 F2 后延迟处理，确保最后的校验文字能够返回并使用
3. **逻辑清晰**: 通过模式标识，清楚区分按键录音和普通录音的处理逻辑

### 🎯 用户体验
1. **自然交互**: 按住 F2 说话，松开后自动发送给 AI
2. **文字准确**: 使用经过 FunASR 校验矫正的最终文字
3. **响应及时**: 松开按键后快速响应，无需手动等待

## 测试方法

### 1. 访问测试页面
- 启动应用：`npm run start`
- 访问：`/test/keyRecordingTest`

### 2. 测试步骤
1. 按住 F2 键开始录音
2. 说一段话（观察实时识别文字）
3. 松开 F2 键
4. 观察控制台日志和 AI 回复

### 3. 观察要点
- 按键录音期间，`state: 1` 的文字被缓存，不触发 AI 请求
- 松开按键后，使用缓存的最终校验文字发送 AI 请求
- 控制台日志显示完整的处理流程

## 关键技术点

### 1. 状态管理
- `isKeyRecordingMode`: WebSocket 层面的按键录音模式标识
- `keyRecordingFinalText`: 缓存按键录音期间的最终校验文字
- `isKeyListening`: UI 层面的按键状态

### 2. 时序控制
- 按键按下：设置模式 → 重置识别 → 延迟启动录音
- 按键松开：关闭模式 → 停止录音 → 延迟处理结果

### 3. 容错机制
- 优先使用缓存的校验文字
- 备用方案：使用当前显示文字
- 超时保护：避免无限等待

## 扩展性

### 支持多种触发方式
- 可以轻松扩展支持其他按键（F3、Space 等）
- 可以添加语音唤醒模式
- 可以支持手势控制

### 自定义处理逻辑
- 可以自定义 AI 请求的处理方式
- 可以添加文字预处理（过滤、格式化等）
- 可以支持多轮对话模式

这个解决方案完美解决了按键录音的时序问题，确保用户获得准确、及时的语音识别和 AI 响应体验。
