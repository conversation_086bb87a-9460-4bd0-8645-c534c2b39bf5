<script setup lang="ts">
import { useRecorder } from './hooks/useRecorder'
import { useSocketStore } from '@/stores/ws'
import { useVoiceRecognition } from './hooks/useVoiceRecognition'
import { watch, onUnmounted, provide, ref, onMounted } from 'vue'
import { useTTS } from '@/hooks/useTTS'
import { useAiRequest } from '@/hooks/useAiRequest'
import { RouterView } from 'vue-router'
import gsap from 'gsap'
import { playAudio } from '@/utils/audioPlayer'
import { dialogSystem } from '@/assets/ai-system/system'
const { isRecording, recOpen, recStart, recStop, recPause, recResume } = useRecorder()
const socketStore = useSocketStore()
const {
  isWakeWordDetected,
  handleVoiceText,
  startWatching,
  figureOut,
  isHandlingCommand,
  lastText,
  startOnceTalk,
  startSPTalk,
  isEndTrain,
  endSPTalkCountdown,
  isSPTalk,
  inAnalysisPage,
  resetAnalysisPage,
  endSPTalk,
  isEndTalkType
} = useVoiceRecognition()
const { aiRequest } = useAiRequest()
const { startTTS, stopTTS } = useTTS()

// 按键监听状态
const isKeyListening = ref(false)

// 启动websocket连接
socketStore.wsStart()

// 监听 websocket 连接状态
watch(
  () => socketStore.state,
  async (newVal) => {
    if (newVal === 1) {
      try {
        await recOpen()
      } catch (error) {
        console.error('录音启动失败:', error)
      }
    }
  }
)

// 监听 websocket 接收到的文本
watch(
  () => socketStore.lastText,
  (newText) => {
    if (newText) {
      const text = newText
      handleVoiceText(text)
      socketStore.clearSocketText()
    }
  }
)

// #region 首页唤醒已经ai操作

// 唤醒后相关操作处理完成
function wakeWordHandle() {
  // 播放音效让用户知道
  playAudio('./inform.mp3')
  setTimeout(() => {
    // 恢复录音
    recResume()
    // 开始监听
    startWatching()
  }, 500)
}

// 处理用户指令
async function aiDataHandle() {
  // 暂停录音
  // recPause()
  // 调用ai请求
  const aiText = await aiRequest(lastText.value, dialogSystem)
  // recResume()

  return aiText
}

async function aiDataHandle_end() {
  // 恢复处理指令
  socketStore.resetRecognition()

  figureOut()

  // 恢复录音
  // recResume()
}
// #endregion

// #region sp页面下的指令

// function spHandle() {
//   // 暂停录音
//   recPause()
//   // 一次对话结束
//   startOnceTalk.value = false
//   // 恢复录音
//   recResume()
// }

const isOverTalk = ref(false)
// 监听结束训练状态
watch(
  () => isEndTrain.value,
  (newVal) => {
    if (newVal) {
      // 问用户确认结束吗
      isOverTalk.value = true
      userConfirmEndTrain()
    } else {
      let tl = gsap.timeline({
        onComplete: () => {
          isOverTalk.value = false
          // 重置样式
          tl.kill()
        }
      })
      tl.fromTo(
        '.overTalk_content',
        {
          scale: 1,
          opacity: 1
        },
        {
          duration: 0.3,
          scale: 0.5,
          opacity: 0,
          ease: 'power1.out'
        }
      )
      tl.fromTo(
        '.overTalk',
        {
          opacity: 1
        },
        {
          duration: 0.2,
          opacity: 0,
          ease: 'power1.out'
        }
      )
    }
  }
)

async function userConfirmEndTrain() {
  // 暂停录音
  recPause()
  gsap.set('.overTalk,.overTalk_content', { opacity: 1 })
  gsap.fromTo(
    '.overTalk_content',
    {
      scale: 0.5
    },
    {
      duration: 0.2,
      scale: 1,
      ease: 'power1.inOut',
      onComplete: async () => {
        const audio = new Audio('./endTrain.wav') // 替换为你的音频文件路径
        audio.play()
        audio.onended = () => {
          playAudio('./inform.mp3')
          // 恢复录音
          recResume()
          audio.pause() // 暂停音频
          audio.currentTime = 0 // 重置播放时间
          audio.remove() // 销毁音频对象

          // 倒计时5秒，如果用户没有说确认或取消，则取消结束训练
          endSPTalkCountdown()
        }
      }
    }
  )
}

function resetRecognition() {
  socketStore.resetRecognition()
}

// #region 按键监听功能
// 处理按键监听事件
const handleVoiceListening = (event, data) => {
  if (data.action === 'start') {
    isKeyListening.value = true

    // 开始录音
    recStart()
    // 播放提示音
    // playAudio('./inform.mp3')
  } else if (data.action === 'stop') {
    isKeyListening.value = false
    // // 暂停录音
    // if (isRecording.value) {
    recPause()
    // }
  }
}

// 初始化按键监听
onMounted(() => {
  // 监听来自主进程的按键事件
  if (window.electron && window.electron.onVoiceListening) {
    window.electron.onVoiceListening(handleVoiceListening)
  }
})
// #endregion

// 提供唤醒状态
provide('wake', {
  isWakeWordDetected,
  wakeWordHandle,
  isHandlingCommand,
  aiDataHandle,
  aiDataHandle_end,
  startSPTalk,
  startTTS,
  recPause,
  aiRequest,
  startOnceTalk,
  recResume,
  lastText,
  isSPTalk,
  stopTTS,
  isRecording,
  inAnalysisPage,
  resetAnalysisPage,
  endSPTalk,
  isEndTalkType,
  resetRecognition,
  isKeyListening
})

onUnmounted(() => {
  recStop()

  // 清理按键监听事件
  if (window.electron && window.electron.removeVoiceListening) {
    window.electron.removeVoiceListening(handleVoiceListening)
  }
})
</script>
<template>
  <RouterView />

  <div class="overTalk" v-show="isOverTalk">
    <div class="overTalk_content">
      <div class="overTalk_content_img">
        <img src="@/assets/images/robot.png" alt="" />
      </div>
      <div class="overTalk_content_title">确认要结束训练吗?</div>
      <div class="overTalk_content_message">请说<span>“确认”</span>或<span>“取消”</span></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.overTalk {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.9);

  .overTalk_content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    width: 751px;
    height: 579px;
    padding-top: 150px;
    background: url('@/assets/images/overDialog.png') no-repeat center center;
    background-size: 100% 100%;
    .overTalk_content_img {
      position: absolute;
      top: -200px;
      left: 50%;
      transform: translateX(-50%);
      width: 378px;
      height: 378px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .overTalk_content_title,
    .overTalk_content_message {
      font-family: PingFang SC;
      font-weight: bold;
      font-size: 60px;
      color: #1a3950;
      line-height: 85px;
      letter-spacing: 3px;
    }
    .overTalk_content_message {
      margin-top: 85px;
      span {
        background: linear-gradient(90deg, #00ffee, #0055ff); /* 渐变背景 */
        -webkit-background-clip: text; /* 仅对文字应用背景 */
        -webkit-text-fill-color: transparent; /* 使文字填充颜色透明 */
      }
    }
  }
}
</style>
