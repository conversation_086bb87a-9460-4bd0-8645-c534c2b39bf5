# node-cachedir

Provides a directory where the OS wants you to store cached files.

## Installation

```sh
npm install --save cachedir
```

## Usage

```javascript
const cachedir = require('cachedir')

console.log(cachedir('myapp'))
// e.g.
//=> '/Users/<USER>/Library/Caches/myapp'
//=> '/home/<USER>/.cache/myapp'
//=> 'C:\Users\<USER>\AppData\Local\myapp\Cache'
```

## API

### `cachedir(id)`

- `id` (`string`, required)
- returns `string`

Return path to an appropriate place to store cache files.
