<template>
  <div class="key-recording-test">
    <div class="header">
      <h1>按键录音测试</h1>
      <p>按住 F2 键进行语音录制，松开后自动发送给 AI</p>
    </div>

    <div class="status-panel">
      <div class="status-item">
        <span class="label">按键状态:</span>
        <span class="value" :class="{ active: isKeyListening }">
          {{ isKeyListening ? '按键按下' : '按键松开' }}
        </span>
      </div>

      <div class="status-item">
        <span class="label">实时识别文字:</span>
        <span class="value">{{ listenText || '暂无' }}</span>
      </div>

      <div class="status-item">
        <span class="label">WebSocket原始数据:</span>
        <span class="value">
          <pre>{{ JSON.stringify(socketStore.lastData, null, 2) }}</pre>
        </span>
      </div>

      <div class="status-item">
        <span class="label">AI 回复:</span>
        <span class="value">{{ aiResponse || '暂无' }}</span>
      </div>
    </div>

    <div class="instructions">
      <h3>测试说明：</h3>
      <ul>
        <li>按住 F2 键开始录音</li>
        <li>说话时会看到实时识别的文字</li>
        <li>松开 F2 键后，系统会使用最终校验的文字发送给 AI</li>
        <li>观察控制台日志，查看详细的处理过程</li>
      </ul>
    </div>

    <div class="visual-indicator" :class="{ listening: isKeyListening }">
      <div class="circle">
        <div class="inner-circle"></div>
      </div>
      <div class="text">
        {{ isKeyListening ? '正在录音...' : '按住 F2 开始录音' }}
      </div>
    </div>

    <div class="debug-info">
      <h3>调试信息：</h3>
      <div class="debug-item"><strong>WebSocket 状态:</strong> {{ getSocketStatus() }}</div>
      <div class="debug-item">
        <strong>最后收到的数据:</strong>
        <pre>{{ JSON.stringify(socketStore.lastData, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useListen } from '../talk/hooks/useListen'
import { useSocketStore } from '@/stores/ws'

// 使用语音监听 hook
const { listenText, isKeyListening, aiResponse } = useListen()

// 获取 WebSocket 状态
const socketStore = useSocketStore()

// 获取 WebSocket 状态文本
const getSocketStatus = () => {
  switch (socketStore.state) {
    case 0:
      return '连接关闭'
    case 1:
      return '连接成功'
    case 2:
      return '连接失败'
    default:
      return '未知状态'
  }
}
</script>

<style scoped>
.key-recording-test {
  padding: 40px;
  max-width: 1000px;
  margin: 0 auto;
  font-family: 'PingFang SC', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.status-panel {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 40px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.status-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #333;
  font-size: 16px;
  min-width: 120px;
}

.value {
  font-size: 16px;
  color: #666;
  padding: 8px 16px;
  border-radius: 20px;
  background: #e9ecef;
  transition: all 0.3s ease;
  flex: 1;
  margin-left: 20px;
  word-break: break-all;
}

.value.active {
  background: #28a745;
  color: white;
  font-weight: 600;
}

.instructions {
  background: #fff;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 40px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.instructions h3 {
  color: #333;
  margin-bottom: 20px;
}

.instructions ul {
  list-style: none;
  padding: 0;
}

.instructions li {
  padding: 10px 0;
  color: #666;
  position: relative;
  padding-left: 25px;
}

.instructions li::before {
  content: '•';
  color: #007bff;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.visual-indicator {
  text-align: center;
  padding: 40px;
  margin-bottom: 40px;
}

.circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #e9ecef;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.listening .circle {
  background: #28a745;
  animation: pulse 2s infinite;
}

.inner-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #fff;
  transition: all 0.3s ease;
}

.listening .inner-circle {
  background: #fff;
  animation: innerPulse 1s infinite alternate;
}

.text {
  font-size: 18px;
  font-weight: 600;
  color: #666;
  transition: all 0.3s ease;
}

.listening .text {
  color: #28a745;
}

.debug-info {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.debug-info h3 {
  color: #333;
  margin-bottom: 20px;
}

.debug-item {
  margin-bottom: 15px;
  color: #666;
}

.debug-item strong {
  color: #333;
}

.debug-item pre {
  background: #fff;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ddd;
  margin-top: 5px;
  font-size: 12px;
  overflow-x: auto;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(40, 167, 69, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

@keyframes innerPulse {
  0% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1.2);
  }
}
</style>
