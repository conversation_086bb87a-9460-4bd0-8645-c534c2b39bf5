import { createRouter, createWebHashHistory } from 'vue-router'
import layout from '@/layout/index.vue'
import { defineAsyncComponent } from 'vue'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: defineAsyncComponent(() => import('@/views/home/<USER>'))
    },
    {
      path: '/talk',
      name: 'talk',
      component: defineAsyncComponent(() => import('@/views/talk/index.vue'))
    },
    {
      path: '/sp',
      component: layout,
      redirect: '/spTrain',
      children: [
        {
          path: '/spTrain',
          name: 'spTrain',
          component: defineAsyncComponent(() => import('@/views/sp/index.vue'))
        }
      ]
    },
    {
      path: '/analysis',
      component: layout,
      redirect: '/analysisPage',
      children: [
        {
          path: '/analysisPage',
          name: 'analysis',
          component: defineAsyncComponent(() => import('@/views/analysis/index.vue'))
        }
      ]
    },
    {
      path: '/test',
      component: layout,
      redirect: '/voiceTest',
      children: [
        {
          path: '/voiceTest',
          name: 'voiceTest',
          component: defineAsyncComponent(() => import('@/views/test/VoiceTest.vue'))
        },
        {
          path: '/keyRecordingTest',
          name: 'keyRecordingTest',
          component: defineAsyncComponent(() => import('@/views/test/KeyRecordingTest.vue'))
        }
      ]
    }
  ]
})

export default router
