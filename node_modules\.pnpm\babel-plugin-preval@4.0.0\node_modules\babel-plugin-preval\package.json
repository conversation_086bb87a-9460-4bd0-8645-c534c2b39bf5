{"name": "babel-plugin-preval", "version": "4.0.0", "description": "Pre-evaluate code at build-time", "engines": {"node": ">=8", "npm": ">=6"}, "main": "dist/index.js", "scripts": {"build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "files": ["dist", "macro.js"], "keywords": ["babel", "babel-plugin", "eval", "precompile", "babel-plugin-macros"], "author": "<PERSON> <PERSON> <<EMAIL>> (http://kentcdodds.com/)", "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.2", "babel-plugin-macros": "^2.6.1", "require-from-string": "^2.0.2"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^7.0.3", "kcd-scripts": "^1.11.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["kcd-scripts/babel"]}, "repository": {"type": "git", "url": "https://github.com/kentcdodds/babel-plugin-preval.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-preval/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-preval#readme"}