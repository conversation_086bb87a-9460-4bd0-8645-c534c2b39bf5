import Recorder from 'recorder-core'
import 'recorder-core/src/engine/wav'
import 'recorder-core/src/extensions/waveview'
import { ref } from 'vue'
import { useSocketStore } from '@/stores/ws'

export function useRecorder() {
  let rec
  let wave
  var sampleBuf = new Int16Array()

  const isRecording = ref('0') // 0:未开始 1:录音中 2:录音结束 3:录音暂停
  // 获取录音权限以及音频配置
  async function getMediaStream() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1, // 单声道
          echoCancellation: true, // 回声消除
          noiseSuppression: true, // 噪声抑制
          autoGainControl: true // 自动调节音量
          // sampleRate: 44100 // 设置固定采样率
        }
      })
      return stream
    } catch (error) {
      console.error('获取音频流失败:', error)
      return null
    }
  }

  const open = async (success) => {
    // 如果已存在实例，先关闭
    if (rec) {
      rec.close()
      rec = null
    }

    try {
      // 先获取媒体流
      const stream = await getMediaStream()
      if (!stream) {
        throw new Error('无法获取音频流')
      }

      rec = new Recorder({
        type: 'wav',
        sampleRate: 16000,
        bitRate: 16,
        onProcess: recProcess,
        sourceStream: stream, // 直接使用获取到的流
        audioTrackSet: {
          channelCount: 1,
          echoCancellation: true, // 回声消除
          noiseSuppression: true, // 噪声抑制
          autoGainControl: true // 自动调节音量
        }
      })

      rec.open(
        () => {
          console.log('录音初始化成功')
          success && success()
        },
        (error) => {
          console.error('录音初始化失败:', error)
          // 如果失败，尝试使用默认配置，针对蓝牙耳机适配
          const defaultConfig = {
            type: 'wav',
            sampleRate: 16000,
            bitRate: 16,
            onProcess: recProcess
          }
          rec = new Recorder(defaultConfig)
          rec.open(success, (err) => {
            console.error('录音完全初始化失败:', err)
          })
        }
      )
    } catch (error) {
      console.error('录音设备初始化失败:', error)
      if (success) success()
    }
  }

  // 开始录音
  const recStart = () => {
    if (isRecording.value === '1') return
    console.log('执行次数')
    rec.start()
    isRecording.value = '1'
  }

  // 停止录音
  const recStop = () => {
    var chunk_size = new Array(5, 10, 5)
    var request = {
      chunk_size: chunk_size,
      wav_name: 'h5',
      is_speaking: false,
      chunk_interval: 10,
      mode: '2pass-offline'
    }
    console.log(request)
    if (sampleBuf.length > 0) {
      useSocketStore().wsSend(sampleBuf)
      console.log('sampleBuf.length' + sampleBuf.length)
      sampleBuf = new Int16Array()
    }
    useSocketStore().wsSend(JSON.stringify(request))
    rec.stop()
    isRecording.value = '2'
  }

  // 开始录音
  const recOpen = async () => {
    try {
      await open(() => {})
    } catch (error) {
      console.error('打开录音失败:', error)
      isRecording.value = '2'
    }
  }

  // 录音处理
  function recProcess(buffer, powerLevel, bufferDuration, bufferSampleRate) {
    if (isRecording.value === '1') {
      var data_48k = buffer[buffer.length - 1]
      var array_48k = new Array(data_48k)
      var data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data

      sampleBuf = Int16Array.from([...sampleBuf, ...data_16k])
      var chunk_size = 960 // for asr chunk_size [5, 10, 5]
      while (sampleBuf.length >= chunk_size) {
        let sendBuf = sampleBuf.slice(0, chunk_size)
        sampleBuf = sampleBuf.slice(chunk_size, sampleBuf.length)
        useSocketStore().wsSend(sendBuf)
      }
    }
    wave && wave.input(buffer[buffer.length - 1], powerLevel, bufferSampleRate)
  }
  // 暂停录音
  function recPause() {
    if (isRecording.value !== '1') return
    rec.pause()
    console.log('暂停录音')
    isRecording.value = '3'
  }
  // 恢复录音
  function recResume() {
    console.log('恢复录音')
    isRecording.value = '1'
    rec?.resume()
  }

  return {
    isRecording,
    recOpen,
    recStart,
    recStop,
    recPause,
    recResume
  }
}
