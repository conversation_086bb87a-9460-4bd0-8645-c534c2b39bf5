import axios from 'axios'
import { ref } from 'vue'

// 创建一个单例存储
const globalState = {
  messages: ref([]),
  spSystem: ref('')
}

export const talkRecord = ref([])
export const useAiRequest = () => {
  // AI智能问答
  const aiRequest = (userText, systemText) => {
    return new Promise((resolve, reject) => {
      axios
        .post('http://192.168.1.224:11434/api/chat', {
          // .post('http://192.168.1.223:10086/api/chat', {
          model: 'qwen2.5:14b',
          messages: [
            {
              role: 'system',
              content: systemText
            },
            { role: 'user', content: userText }
          ],
          options: {
            stream: false
          }
        })
        .then((res) => {
          if (res.status === 200) {
            // 处理流式响应
            const lines = res.data.split('\n').filter((line) => line.trim())
            let fullMessage = ''

            lines.forEach((line) => {
              try {
                const parsed = JSON.parse(line)
                if (parsed.message && parsed.message.content) {
                  // 将内容中的换行符替换为 <br> 标签
                  // const formattedContent = parsed.message.content.replace(/\n/g, '<br>')
                  fullMessage += parsed.message.content // 直接添加格式化后的内容
                }
              } catch (e) {
                console.error('解析JSON失败:', e)
              }
            })
            // 进行文字转语音
            //   startTTS(fullMessage)
            console.log('完整消息:', fullMessage)
            talkRecord.value.push({
              user: userText,
              ai: fullMessage
            })
            resolve(fullMessage) // 返回格式化后的消息
          }
        })
        .catch((error) => {
          console.log(error)
          reject(error)
        })
    })
  }

  const setSpSystem = (newVal) => {
    globalState.spSystem.value = newVal
    globalState.messages.value = [
      {
        role: 'system',
        content: globalState.spSystem.value
      }
    ]
  }

  const aiSpCase = (userText) => {
    console.log(globalState.messages.value, 'messages')

    // 添加用户新消息到历史记录
    globalState.messages.value.push({
      role: 'user',
      content: userText
    })

    return new Promise((resolve, reject) => {
      axios
        .post('http://192.168.1.223:10086/api/chat', {
          model: 'qwen2.5:14b',
          messages: globalState.messages.value,
          options: {
            // maxTokens: 8000,
            temperature: 0.3,
            top_k: 30,
            top_p: 0.4,
            num_ctx: 3000
          }
        })
        .then((res) => {
          if (res.status === 200) {
            const lines = res.data.split('\n').filter((line) => line.trim())
            let fullMessage = ''

            lines.forEach((line) => {
              try {
                const parsed = JSON.parse(line)
                if (parsed.message && parsed.message.content) {
                  fullMessage += parsed.message.content
                }
              } catch (e) {
                console.error('解析JSON失败:', e)
              }
            })

            // 将AI的回复添加到历史记录中
            globalState.messages.value.push({
              role: 'assistant',
              content: fullMessage
            })

            console.log('完整消息:', fullMessage)
            resolve(fullMessage)
          }
        })
        .catch((error) => {
          console.log(error)
          reject(error)
        })
    })
  }
  return { aiRequest, aiSpCase, setSpSystem }
}
