<script setup>
import { watch } from 'vue'
import { useTypewriter } from '@/hooks/useTypewriter'

const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  state: {
    type: String,
    default: ''
  }
})
const { displayedText, startTypeWriterEffect } = useTypewriter()
watch(
  () => props.state,
  (state) => {
    if (state === 'response') {
      startTypeWriterEffect(props.text, 40)
    }
  }
)
</script>
<template>
  <div class="response-container">
    <div class="response-text">{{ displayedText }}</div>
    <img class="response-photo" src="@/assets/images/response-photo.png" alt="" />
  </div>
</template>
<style scoped lang="scss">
.response-container {
  display: flex;
  align-items: flex-end;
  padding-top: 44px;
  padding-left: 53px;
  .response-text {
    width: 760px;
    height: 452px;
    padding: 35px 50px;
    background: #466488;
    border-radius: 60px 60px 0px 60px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 30px;
    color: #ffffff;
    line-height: 44px;
  }
  .response-photo {
    margin-left: 10px;
    width: 80px;
    height: 80px;
  }
}
</style>
