<script setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useWebRTC } from '@/hooks/useWebRTC'
import Loading from '@/components/Animation/loading.vue'
import Listen from './components/Listen.vue'
import Response from './components/Response.vue'
import Record from './components/Record.vue'
import { useListen } from './hooks/useListen'
const { start, stop, loading, sendMsg, videoRef, audioRef } = useWebRTC()

const { isKeyListening, listenText, aiResponse } = useListen()
const talkStatus = ref('tip')

watch(isKeyListening, () => {
  if (isKeyListening.value) {
    talkStatus.value = 'listening'
  }
})
watch(aiResponse, async () => {
  if (aiResponse.value) {
    await sendMsg(aiResponse.value)
    talkStatus.value = 'response'
  }
})

function openRecord() {
  if (window.electron && window.electron.onShortcut) {
    window.electron.onShortcut(() => {
      talkStatus.value = talkStatus.value === 'recording' ? 'response' : 'recording'
    })
  }
}
// #region 生命周期
onMounted(() => {
  start()
  openRecord()
})
onUnmounted(() => {
  stop()
})
// #endregion
</script>
<template>
  <div class="talk-container">
    <div v-if="loading" class="loading-box">
      <Loading class="loadingComponent" />
      <div>数字人正在加载中...</div>
    </div>
    <img class="logo" src="@/assets/images/logo.png" alt="" />
    <div v-if="talkStatus === 'response' || talkStatus === 'recording'" class="record-box">
      <img src="@/assets/images/record-icon.png" alt="" />
      <span>对话记录</span>
    </div>
    <div class="digitalHuman-box">
      <video ref="videoRef" autoplay></video>
      <audio ref="audioRef" autoplay></audio>
    </div>
    <div class="operate-box">
      <div v-show="talkStatus === 'tip'" class="tip">按住 F2 键开始语音对话</div>
      <Listen v-show="talkStatus === 'listening'" :text="listenText" />

      <Response v-show="talkStatus === 'response'" :text="aiResponse" :state="talkStatus" />
      <Record v-show="talkStatus === 'recording'" />
    </div>
  </div>
</template>
<style scoped lang="scss">
.talk-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .logo {
    position: absolute;
    left: 85px;
    top: 76px;
  }
  .record-box {
    position: absolute;
    right: 76px;
    top: 77px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    img {
      width: 60px;
      height: 60px;
    }
    span {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 36px;
      color: #466488;
    }
  }
  .loading-box {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 999;
    background: url('@/assets/images/loading-bg.png');
    .loadingComponent {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -100%);
    }
    div {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, 90%);
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 54px;
      color: #24365f;
    }
  }
  .digitalHuman-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9;
    video {
      width: 100%;
      height: 100%;
    }
  }
  .operate-box {
    position: absolute;
    bottom: 86px;
    left: 60px;
    width: 960px;
    height: 540px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 4px 4px 0px rgba(181, 207, 253, 0.8);
    border-radius: 60px 60px 60px 60px;
    z-index: 99;
    .tip {
      height: 90%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 56px;
      color: #112044;
    }
  }
}
</style>
