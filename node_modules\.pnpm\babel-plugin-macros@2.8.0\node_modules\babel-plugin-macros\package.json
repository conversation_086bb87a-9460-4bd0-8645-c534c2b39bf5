{"name": "babel-plugin-macros", "version": "2.8.0", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": "<PERSON> <PERSON> <<EMAIL>> (http://kentcdodds.com/)", "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.2", "cosmiconfig": "^6.0.0", "resolve": "^1.12.0"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/parser": "^7.7.3", "@babel/types": "^7.7.2", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^7.0.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^7.3.0", "kcd-scripts": "^1.11.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme"}