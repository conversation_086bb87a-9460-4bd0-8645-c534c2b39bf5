import { onMounted, ref, watch } from 'vue'
import { useRecorder } from '@/hooks/useRecorder'
import { useAiRequest } from '@/hooks/useAiRequest'
import { useSocketStore } from '@/stores/ws'
import { dialogSystem } from '@/assets/ai-system/system'
const { recOpen, recStart, recStop } = useRecorder()

export function useListen() {
  const socketStore = useSocketStore()
  const { aiRequest } = useAiRequest()
  const listenText = ref('')
  const aiResponse = ref('')
  // 监听 websocket 连接状态
  watch(
    () => socketStore.state,
    async (newVal) => {
      if (newVal === 1) {
        try {
          await recOpen()
        } catch (error) {
          console.error('录音启动失败:', error)
        }
      }
    }
  )

  // 监听 websocket 接收到的文本
  watch(
    () => socketStore.lastData,
    (newData) => {
      if (newData) {
        listenText.value = newData.text
        if (newData.state === 1) {
          console.log('最终识别结果:', listenText.value)
          // 进行AI请求
          aiRequestHandle()
        }
        //   handleVoiceText(text)
        //   socketStore.clearSocketText()
      }
    }
  )

  function aiRequestHandle() {
    aiRequest(listenText.value, dialogSystem).then((res) => {
      console.log(res, 'ai回复消息')
      aiResponse.value = res
    })
  }

  // 按键监听状态
  const isKeyListening = ref(false)
  // #region 按键监听功能
  // 处理按键监听事件
  const handleVoiceListening = (event, data) => {
    if (data.action === 'start') {
      isKeyListening.value = true
      // 开始录音
      recStart()
    } else if (data.action === 'stop') {
      isKeyListening.value = false
      console.log(listenText.value)

      // 暂停录音
      recStop()
    }
  }

  // 初始化按键监听
  onMounted(() => {
    // 监听来自主进程的按键事件
    if (window.electron && window.electron.onVoiceListening) {
      window.electron.onVoiceListening(handleVoiceListening)
    }
  })

  // #endregion
  socketStore.wsStart()
  return {
    listenText,
    isKeyListening,
    aiResponse
  }
}
