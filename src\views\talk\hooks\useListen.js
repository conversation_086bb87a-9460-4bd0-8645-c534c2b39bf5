import { onMounted, ref, watch } from 'vue'
import { useRecorder } from '@/hooks/useRecorder'
import { useAiRequest } from '@/hooks/useAiRequest'
import { useSocketStore } from '@/stores/ws'
import { dialogSystem } from '@/assets/ai-system/system'
const { recOpen, recStart, recStop } = useRecorder()

export function useListen() {
  const socketStore = useSocketStore()
  const { aiRequest } = useAiRequest()
  const listenText = ref('')
  const aiResponse = ref('')

  // 按键录音状态
  const isKeyListening = ref(false)
  // 按键录音期间缓存的最终文字
  const keyRecordingFinalText = ref('')
  // 监听 websocket 连接状态
  watch(
    () => socketStore.state,
    async (newVal) => {
      if (newVal === 1) {
        try {
          await recOpen()
        } catch (error) {
          console.error('录音启动失败:', error)
        }
      }
    }
  )

  // 监听 websocket 接收到的文本
  watch(
    () => socketStore.lastData,
    (newData) => {
      if (newData && newData.text) {
        listenText.value = newData.text

        if (newData.state === 1) {
          console.log('最终识别结果:', listenText.value, '按键录音模式:', newData.isKeyRecording)

          // 如果是按键录音模式，缓存最终文字，不立即发送AI请求
          if (newData.isKeyRecording) {
            keyRecordingFinalText.value = newData.text
            console.log('按键录音模式 - 缓存最终文字:', keyRecordingFinalText.value)
          } else {
            // 非按键录音模式，正常进行AI请求
            aiRequestHandle()
          }
        }
      }
    }
  )

  function aiRequestHandle(text = listenText.value) {
    aiRequest(text, dialogSystem).then((res) => {
      console.log(res, 'ai回复消息')
      aiResponse.value = res
    })
  }

  // #region 按键监听功能
  // 处理按键监听事件
  const handleVoiceListening = (_, data) => {
    if (data.action === 'start') {
      isKeyListening.value = true
      keyRecordingFinalText.value = '' // 清空缓存
      console.log('开始按键录音')

      // 设置按键录音模式
      socketStore.setKeyRecordingMode(true)

      // 重置识别状态
      socketStore.resetRecognition()

      // 短暂延迟后开始录音
      setTimeout(() => {
        recStart()
      }, 100)
    } else if (data.action === 'stop') {
      isKeyListening.value = false
      console.log('停止按键录音，当前文字:', listenText.value)
      console.log('缓存的最终文字:', keyRecordingFinalText.value)

      // 关闭按键录音模式
      socketStore.setKeyRecordingMode(false)

      // 停止录音
      recStop()

      // 短暂延迟后处理最终结果，确保最后的识别结果能够返回
      setTimeout(() => {
        // 如果有缓存的最终文字，使用缓存的文字进行AI请求
        if (keyRecordingFinalText.value.trim()) {
          console.log('使用缓存的最终文字进行AI请求:', keyRecordingFinalText.value)
          aiRequestHandle(keyRecordingFinalText.value)
        } else if (listenText.value.trim()) {
          // 如果没有缓存的最终文字，使用当前文字
          console.log('使用当前文字进行AI请求:', listenText.value)
          aiRequestHandle(listenText.value)
        } else {
          console.log('没有识别到有效文字')
        }

        // 清空缓存
        keyRecordingFinalText.value = ''
      }, 500) // 等待500ms，确保最后的识别结果能够返回
    }
  }

  // 初始化按键监听
  onMounted(() => {
    // 监听来自主进程的按键事件
    if (window.electron && window.electron.onVoiceListening) {
      window.electron.onVoiceListening(handleVoiceListening)
    }
  })

  // #endregion
  socketStore.wsStart()
  return {
    listenText,
    isKeyListening,
    aiResponse
  }
}
